import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../components/Navbar';
import Modal from '../components/Modal';
import Tooltip from '../components/Tooltip';

export default function ProjectsPage() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [projects] = useState([
    {
      id: 1,
      title: 'Superhero Adventure',
      description: 'Epic action-packed storyboard for a superhero movie',
      scenes: 24,
      status: 'In Progress',
      lastModified: '2 hours ago',
      thumbnail: '🦸‍♂️'
    },
    {
      id: 2,
      title: 'Romantic Comedy',
      description: 'Light-hearted romantic story with comedic elements',
      scenes: 18,
      status: 'Completed',
      lastModified: '1 day ago',
      thumbnail: '💕'
    },
    {
      id: 3,
      title: 'Sci-Fi Thriller',
      description: 'Futuristic thriller with mind-bending plot twists',
      scenes: 32,
      status: 'Planning',
      lastModified: '3 days ago',
      thumbnail: '🚀'
    },
    {
      id: 4,
      title: 'Horror Mystery',
      description: 'Spine-chilling mystery with supernatural elements',
      scenes: 28,
      status: 'In Progress',
      lastModified: '5 days ago',
      thumbnail: '👻'
    }
  ]);
  const router = useRouter();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const res = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (res.ok) {
          const data = await res.json();
          setUser(data);
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error fetching user:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchUser();
  }, [router]);

  const getStatusColor = (status) => {
    switch (status) {
      case 'Completed': return '#0be881';
      case 'In Progress': return '#48dbfb';
      case 'Planning': return '#feca57';
      default: return '#ff6b6b';
    }
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="spinner"></div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container" style={{ paddingTop: '100px' }}>
        <div className="projects-container">
          <div className="projects-header">
            <div>
              <h1>My Projects 📁</h1>
              <button className="cta-primary" onClick={() => setShowModal(true)}>
              ➕ New Project
            </button>
            </div>
          </div>

          <div className="projects-stats">
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-info">
                <span className="stat-number">{projects.length}</span>
                <span className="stat-label">Total Projects</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">✅</div>
              <div className="stat-info">
                <span className="stat-number">{projects.filter(p => p.status === 'Completed').length}</span>
                <span className="stat-label">Completed</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🔄</div>
              <div className="stat-info">
                <span className="stat-number">{projects.filter(p => p.status === 'In Progress').length}</span>
                <span className="stat-label">In Progress</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🎬</div>
              <div className="stat-info">
                <span className="stat-number">{projects.reduce((sum, p) => sum + p.scenes, 0)}</span>
                <span className="stat-label">Total Scenes</span>
              </div>
            </div>
          </div>

          <div className="projects-grid">
            {projects.map((project) => (
              <div key={project.id} className="project-card">
                <div className="project-thumbnail">
                  <span className="project-icon">{project.thumbnail}</span>
                  <div className="project-overlay">
                    <Tooltip text="Edit Project">
                      <button className="overlay-btn">✏️</button>
                    </Tooltip>
                    <Tooltip text="View Details">
                      <button className="overlay-btn">👁️</button>
                    </Tooltip>
                    <Tooltip text="Share Project">
                      <button className="overlay-btn">🔗</button>
                    </Tooltip>
                  </div>
                </div>
                
                <div className="project-content">
                  <div className="project-header">
                    <h3>{project.title}</h3>
                    <span 
                      className="project-status"
                      style={{ backgroundColor: getStatusColor(project.status) }}
                    >
                      {project.status}
                    </span>
                  </div>
                  
                  <p className="project-description">{project.description}</p>
                  
                  <div className="project-meta">
                    <div className="meta-item">
                      <span className="meta-icon">🎭</span>
                      <span>{project.scenes} scenes</span>
                    </div>
                    <div className="meta-item">
                      <span className="meta-icon">⏰</span>
                      <span>{project.lastModified}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <Modal 
          isOpen={showModal} 
          onClose={() => setShowModal(false)}
          title="Create New Project"
        >
          <form className="project-form">
            <div className="form-group">
              <label>Project Title</label>
              <input type="text" placeholder="Enter project title..." />
            </div>
            <div className="form-group">
              <label>Description</label>
              <textarea placeholder="Describe your project..." rows="4"></textarea>
            </div>
            <div className="form-group">
              <label>Category</label>
              <select>
                <option>Action</option>
                <option>Comedy</option>
                <option>Drama</option>
                <option>Horror</option>
                <option>Sci-Fi</option>
                <option>Romance</option>
              </select>
            </div>
            <button type="submit" className="auth-button">
              🚀 Create Project
            </button>
          </form>
        </Modal>
      </div>
    </>
  );
}
