import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../components/Navbar';
import Modal from '../components/Modal';
import Tooltip from '../components/Tooltip';

export default function ProjectsPage() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [projects, setProjects] = useState([]);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image: null
  });
  const [imagePreview, setImagePreview] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [editingProject, setEditingProject] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchUserAndProjects = async () => {
      try {
        // Fetch user data
        const userRes = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (userRes.ok) {
          const userData = await userRes.json();
          setUser(userData);

          // Fetch user's projects
          const projectsRes = await fetch('http://localhost:3001/api/projects', {
            credentials: 'include'
          });
          if (projectsRes.ok) {
            const projectsData = await projectsRes.json();
            setProjects(projectsData.projects);
          }
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchUserAndProjects();
  }, [router]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        image: file
      }));

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleEditProject = (project) => {
    setEditingProject(project);
    setIsEditing(true);
    setFormData({
      title: project.title,
      description: project.description || '',
      image: null
    });

    // Set image preview if project has an uploaded image
    if (project.image && project.image.startsWith('/uploads')) {
      setImagePreview(`http://localhost:3001${project.image}`);
    } else {
      setImagePreview(null);
    }

    setShowModal(true);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.title.trim()) {
      setMessage('Project title is required');
      return;
    }

    setSubmitting(true);
    setMessage('');

    try {
      const formDataToSend = new FormData();
      formDataToSend.append('title', formData.title.trim());
      formDataToSend.append('description', formData.description.trim());

      if (formData.image) {
        formDataToSend.append('image', formData.image);
      } else if (isEditing && !formData.image) {
        // Keep current image if editing and no new image selected
        formDataToSend.append('keepCurrentImage', 'true');
      }

      const url = isEditing
        ? `http://localhost:3001/api/projects/${editingProject.id}`
        : 'http://localhost:3001/api/projects';

      const method = isEditing ? 'PUT' : 'POST';

      const res = await fetch(url, {
        method: method,
        credentials: 'include',
        body: formDataToSend
      });

      const data = await res.json();

      if (res.ok) {
        setMessage(isEditing ? 'Project updated successfully!' : 'Project created successfully!');

        // Reset form
        setFormData({ title: '', description: '', image: null });
        setImagePreview(null);
        setEditingProject(null);
        setIsEditing(false);

        // Refresh projects list
        const projectsRes = await fetch('http://localhost:3001/api/projects', {
          credentials: 'include'
        });
        if (projectsRes.ok) {
          const projectsData = await projectsRes.json();
          setProjects(projectsData.projects);
        }

        // Close modal after a short delay
        setTimeout(() => {
          setShowModal(false);
          setMessage('');
        }, 1500);
      } else {
        setMessage(data.message || `Failed to ${isEditing ? 'update' : 'create'} project`);
      }
    } catch (error) {
      console.error(`Error ${isEditing ? 'updating' : 'creating'} project:`, error);
      setMessage('Connection error. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now - date);
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    return date.toLocaleDateString();
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="spinner"></div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container" style={{ paddingTop: '100px' }}>
        <div className="projects-container">
          <div className="projects-header">
            <div>
              <h1>My Projects 📁</h1>
              <button className="cta-primary" onClick={() => {
                setIsEditing(false);
                setEditingProject(null);
                setFormData({ title: '', description: '', image: null });
                setImagePreview(null);
                setMessage('');
                setShowModal(true);
              }}>
              ➕ &nbsp; New Project
            </button>
            </div>
          </div>

          <div className="projects-stats">
            <div className="stat-card">
              <div className="stat-icon">📊</div>
              <div className="stat-info">
                <span className="stat-number">{projects.length}</span>
                <span className="stat-label">Total Projects</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">✅</div>
              <div className="stat-info">
                <span className="stat-number">{projects.filter(p => p.status === 'Completed').length}</span>
                <span className="stat-label">Completed</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🔄</div>
              <div className="stat-info">
                <span className="stat-number">{projects.filter(p => p.status === 'In Progress').length}</span>
                <span className="stat-label">In Progress</span>
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-icon">🎬</div>
              <div className="stat-info">
                <span className="stat-number">{projects.reduce((sum, p) => sum + (p.scenes || 0), 0)}</span>
                <span className="stat-label">Total Scenes</span>
              </div>
            </div>
          </div>

          <div className="projects-grid">
            {projects.length === 0 ? (
              <div className="empty-state">
                <div className="empty-icon">📁</div>
                <h3>No projects yet</h3>
                <p>Create your first project to get started!</p>
                <button className="cta-primary" onClick={() => setShowModal(true)}>
                  ➕ Create Project
                </button>
              </div>
            ) : (
              projects.map((project) => (
                <div key={project.id} className="project-card">
                  <div className="project-thumbnail">
                    {project.image && project.image.startsWith('/uploads') ? (
                      <img
                        src={`http://localhost:3001${project.image}`}
                        alt={project.title}
                        className="project-image"
                      />
                    ) : project.image && project.image.startsWith('linear-gradient') ? (
                      <div
                        className="project-pattern"
                        style={{ background: project.image }}
                      ></div>
                    ) : (
                      <div className="project-pattern" style={{
                        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
                      }}></div>
                    )}
                    <div className="project-overlay">
                      <Tooltip text="Edit Project">
                        <button className="overlay-btn" onClick={() => handleEditProject(project)}>✏️</button>
                      </Tooltip>
                      <Tooltip text="View Details">
                        <button className="overlay-btn">👁️</button>
                      </Tooltip>
                    </div>
                  </div>

                  <div className="project-content">
                    <div className="project-header">
                      <h3>{project.title}</h3>
                    </div>

                    <span className="project-status in-progress">
                      In Progress
                    </span>

                    <p className="project-description">
                      {project.description || 'No description provided'}
                    </p>

                    <div className="project-meta">
                      <div className="meta-item">
                        <span className="meta-icon">🎭</span>
                        <span>0 scenes</span>
                      </div>
                      <div className="meta-item">
                        <span className="meta-icon">⏰</span>
                        <span>{formatDate(project.updated_at)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>

        <Modal
          isOpen={showModal}
          onClose={() => {
            setShowModal(false);
            setFormData({ title: '', description: '', image: null });
            setImagePreview(null);
            setMessage('');
            setEditingProject(null);
            setIsEditing(false);
          }}
          title={isEditing ? "Edit Project" : "Create New Project"}
        >
          <form className="project-form" onSubmit={handleSubmit}>
            <div className="form-group">
              <label>Project Title *</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter project title..."
                required
              />
            </div>

            <div className="form-group">
              <label>Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe your project..."
                rows="4"
              ></textarea>
            </div>

            <div className="form-group">
              <label>Project Banner Image</label>
              <div className="image-upload-container">
                <input
                  type="file"
                  id="image-upload"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="image-input"
                />
                <label htmlFor="image-upload" className="image-upload-label">
                  {imagePreview ? (
                    <div className="image-preview">
                      <img src={imagePreview} alt="Preview" />
                      <div className="image-overlay">
                        <span>Click to change image</span>
                      </div>
                    </div>
                  ) : (
                    <div className="upload-placeholder">
                      <span className="upload-icon">📷</span>
                      <span>Click to upload banner image</span>
                      <small>Optional - A random pattern will be used if no image is uploaded</small>
                    </div>
                  )}
                </label>
              </div>
            </div>

            {message && (
              <div className={`message ${message.includes('successfully') ? 'success' : 'error'}`}>
                {message}
              </div>
            )}

            <button type="submit" className="auth-button" disabled={submitting}>
              {submitting ? (
                <>
                  <div className="spinner"></div>
                  {isEditing ? 'Updating Project...' : 'Creating Project...'}
                </>
              ) : (
                <>
                  {isEditing ? '✏️ Update Project' : '🚀 Create Project'}
                </>
              )}
            </button>
          </form>
        </Modal>
      </div>
    </>
  );
}
