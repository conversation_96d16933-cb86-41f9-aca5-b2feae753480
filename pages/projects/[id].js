import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import Navbar from '../../components/Navbar';
import Modal from '../../components/Modal';
import Tooltip from '../../components/Tooltip';

export default function ProjectDetailPage() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [project, setProject] = useState(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showSceneModal, setShowSceneModal] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    image: null
  });
  const [imagePreview, setImagePreview] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [message, setMessage] = useState('');
  const [scenes, setScenes] = useState([]);
  const router = useRouter();
  const { id } = router.query;

  useEffect(() => {
    if (!id) return;

    const fetchData = async () => {
      try {
        // Fetch user data
        const userRes = await fetch('http://localhost:3001/api/home', {
          credentials: 'include'
        });
        if (userRes.ok) {
          const userData = await userRes.json();
          setUser(userData);
          
          // Fetch project details
          const projectRes = await fetch(`http://localhost:3001/api/projects/${id}`, {
            credentials: 'include'
          });
          if (projectRes.ok) {
            const projectData = await projectRes.json();
            setProject(projectData.project);
          } else {
            router.push('/projects');
          }
        } else {
          router.push('/login');
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [id, router]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleImageChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prev => ({
        ...prev,
        image: file
      }));
      
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleEditProject = () => {
    setFormData({
      title: project.title,
      description: project.description || '',
      image: null
    });
    
    if (project.image && project.image.startsWith('/uploads')) {
      setImagePreview(`http://localhost:3001${project.image}`);
    } else {
      setImagePreview(null);
    }
    
    setShowEditModal(true);
  };

  const handleUpdateProject = async (e) => {
    e.preventDefault();
    
    if (!formData.title.trim()) {
      setMessage('Project title is required');
      return;
    }

    setSubmitting(true);
    setMessage('');

    try {
      const formDataToSend = new FormData();
      formDataToSend.append('title', formData.title.trim());
      formDataToSend.append('description', formData.description.trim());
      
      if (formData.image) {
        formDataToSend.append('image', formData.image);
      } else {
        formDataToSend.append('keepCurrentImage', 'true');
      }

      const res = await fetch(`http://localhost:3001/api/projects/${id}`, {
        method: 'PUT',
        credentials: 'include',
        body: formDataToSend
      });

      const data = await res.json();
      
      if (res.ok) {
        setMessage('Project updated successfully!');
        
        // Update project state
        setProject(prev => ({
          ...prev,
          title: formData.title.trim(),
          description: formData.description.trim(),
          image: data.imagePath || prev.image
        }));
        
        // Reset form
        setFormData({ title: '', description: '', image: null });
        setImagePreview(null);
        
        setTimeout(() => {
          setShowEditModal(false);
          setMessage('');
        }, 1500);
      } else {
        setMessage(data.message || 'Failed to update project');
      }
    } catch (error) {
      console.error('Error updating project:', error);
      setMessage('Connection error. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <>
        <Navbar />
        <div className="container">
          <div className="spinner"></div>
        </div>
      </>
    );
  }

  if (!project) {
    return (
      <>
        <Navbar />
        <div className="container" style={{ paddingTop: '100px' }}>
          <div className="error-state">
            <h2>Project not found</h2>
            <button className="cta-primary" onClick={() => router.push('/projects')}>
              ← Back to Projects
            </button>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Navbar />
      <div className="container" style={{ paddingTop: '100px' }}>
        <div className="project-detail-container">
          {/* Project Header */}
          <div className="project-detail-header">
            <button className="back-btn" onClick={() => router.push('/projects')}>
              ← Back to Projects
            </button>
            
            <div className="project-actions">
              <Tooltip text="Edit Project">
                <button className="btn-secondary" onClick={handleEditProject}>
                  ✏️ Edit Project
                </button>
              </Tooltip>
            </div>
          </div>

          {/* Project Banner */}
          <div className="project-banner">
            {project.image && project.image.startsWith('/uploads') ? (
              <img 
                src={`http://localhost:3001${project.image}`} 
                alt={project.title}
                className="banner-image"
              />
            ) : project.image && project.image.startsWith('linear-gradient') ? (
              <div 
                className="banner-pattern"
                style={{ background: project.image }}
              ></div>
            ) : (
              <div className="banner-pattern" style={{ 
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' 
              }}></div>
            )}
            
            <div className="banner-overlay">
              <div className="project-info">
                <h1>{project.title}</h1>
                <div className="project-meta-info">
                  <span className="meta-badge">
                    🎭 {scenes.length} scenes
                  </span>
                  <span className="meta-badge">
                    📅 Created {formatDate(project.created_at)}
                  </span>
                  <span className="meta-badge">
                    ⏰ Updated {formatDate(project.updated_at)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Project Content */}
          <div className="project-detail-content">
            <div className="content-section">
              <h2>Description</h2>
              <p className="project-description-full">
                {project.description || 'No description provided for this project.'}
              </p>
            </div>

            <div className="content-section">
              <div className="section-header">
                <h2>Scenes ({scenes.length})</h2>
                <button className="cta-primary" onClick={() => setShowSceneModal(true)}>
                  ➕ Create Scene
                </button>
              </div>
              
              {scenes.length === 0 ? (
                <div className="empty-scenes">
                  <div className="empty-icon">🎬</div>
                  <h3>No scenes yet</h3>
                  <p>Start building your storyboard by creating your first scene!</p>
                  <button className="cta-primary" onClick={() => setShowSceneModal(true)}>
                    🎬 Create First Scene
                  </button>
                </div>
              ) : (
                <div className="scenes-grid">
                  {/* Scenes will be displayed here */}
                  <p>Scenes functionality coming soon...</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Edit Project Modal */}
        <Modal 
          isOpen={showEditModal} 
          onClose={() => {
            setShowEditModal(false);
            setFormData({ title: '', description: '', image: null });
            setImagePreview(null);
            setMessage('');
          }}
          title="Edit Project"
        >
          <form className="project-form" onSubmit={handleUpdateProject}>
            <div className="form-group">
              <label>Project Title *</label>
              <input 
                type="text" 
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                placeholder="Enter project title..." 
                required
              />
            </div>
            
            <div className="form-group">
              <label>Description</label>
              <textarea 
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Describe your project..." 
                rows="4"
              ></textarea>
            </div>
            
            <div className="form-group">
              <label>Project Banner Image</label>
              <div className="image-upload-container">
                <input 
                  type="file" 
                  id="image-upload-edit"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="image-input"
                />
                <label htmlFor="image-upload-edit" className="image-upload-label">
                  {imagePreview ? (
                    <div className="image-preview">
                      <img src={imagePreview} alt="Preview" />
                      <div className="image-overlay">
                        <span>Click to change image</span>
                      </div>
                    </div>
                  ) : (
                    <div className="upload-placeholder">
                      <span className="upload-icon">📷</span>
                      <span>Click to upload banner image</span>
                      <small>Current image will be kept if no new image is uploaded</small>
                    </div>
                  )}
                </label>
              </div>
            </div>

            {message && (
              <div className={`message ${message.includes('successfully') ? 'success' : 'error'}`}>
                {message}
              </div>
            )}
            
            <button type="submit" className="auth-button" disabled={submitting}>
              {submitting ? (
                <>
                  <div className="spinner"></div>
                  Updating Project...
                </>
              ) : (
                <>
                  ✏️ Update Project
                </>
              )}
            </button>
          </form>
        </Modal>

        {/* Create Scene Modal */}
        <Modal 
          isOpen={showSceneModal} 
          onClose={() => setShowSceneModal(false)}
          title="Create New Scene"
        >
          <div className="scene-form">
            <p>Scene creation functionality will be implemented next!</p>
            <button className="auth-button" onClick={() => setShowSceneModal(false)}>
              Close
            </button>
          </div>
        </Modal>
      </div>
    </>
  );
}
