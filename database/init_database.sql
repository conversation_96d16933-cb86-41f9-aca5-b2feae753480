-- Creative Storyboard Database Initialization Script
-- This script creates all necessary tables for the application

-- Create database if it doesn't exist (uncomment if needed)
-- CREATE DATABASE IF NOT EXISTS creative_storyboard;
-- USE creative_storyboard;

-- Create users table (if it doesn't already exist)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(128) NOT NULL,
    description VARCHAR(4000),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    image VARCHAR(256),
    
    -- Foreign key constraint to users table
    CONSTRAINT fk_projects_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE 
        ON UPDATE CASCADE,
    
    -- Indexes for better query performance
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_title (title)
);

-- Add table comments for documentation
ALTER TABLE users 
    COMMENT = 'Stores user account information for authentication and authorization';

ALTER TABLE projects 
    COMMENT = 'Stores project information created by users in the Creative Storyboard application';

-- Add column comments for better documentation
ALTER TABLE users 
    MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing user ID',
    MODIFY COLUMN username VARCHAR(50) NOT NULL UNIQUE COMMENT 'Unique username for login',
    MODIFY COLUMN email VARCHAR(100) NOT NULL UNIQUE COMMENT 'Unique email address for login',
    MODIFY COLUMN password VARCHAR(255) NOT NULL COMMENT 'Hashed password using bcrypt',
    MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when user account was created',
    MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when user account was last updated';

ALTER TABLE projects 
    MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing project ID',
    MODIFY COLUMN user_id INT NOT NULL COMMENT 'Foreign key reference to users.id - owner of the project',
    MODIFY COLUMN title VARCHAR(128) NOT NULL COMMENT 'Project title, maximum 128 characters',
    MODIFY COLUMN description VARCHAR(4000) COMMENT 'Project description, maximum 4000 characters',
    MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when project was created',
    MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when project was last updated',
    MODIFY COLUMN image VARCHAR(256) COMMENT 'File path to project image uploaded by user, maximum 256 characters';

-- Display success message
SELECT 'Database tables created successfully!' as message;
