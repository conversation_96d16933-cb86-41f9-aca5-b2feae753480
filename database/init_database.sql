-- Creative Storyboard Database Initialization Script
-- This script creates all necessary tables for the application

-- Create database if it doesn't exist (uncomment if needed)
-- CREATE DATABASE IF NOT EXISTS creative_storyboard;
-- USE creative_storyboard;

-- Create users table (if it doesn't already exist)
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing user ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT 'Unique username for login',
    email VARCHAR(255) NOT NULL UNIQUE COMMENT 'Unique email address for login',
    password VARCHAR(255) NOT NULL COMMENT 'Hashed password using bcrypt',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when user account was created',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when user account was last updated'
) COMMENT = 'Stores user account information for authentication and authorization';

-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing project ID',
    user_id INT NOT NULL COMMENT 'Foreign key reference to users.id - owner of the project',
    title VARCHAR(128) NOT NULL COMMENT 'Project title, maximum 128 characters',
    description VARCHAR(4000) COMMENT 'Project description, maximum 4000 characters',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when project was created',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when project was last updated',
    image VARCHAR(256) COMMENT 'File path to project image uploaded by user, maximum 256 characters',

    -- Foreign key constraint to users table
    CONSTRAINT fk_projects_user_id
        FOREIGN KEY (user_id)
        REFERENCES users(id)
        ON DELETE CASCADE
        ON UPDATE CASCADE,

    -- Indexes for better query performance
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at),
    INDEX idx_title (title)
) COMMENT = 'Stores project information created by users in the Creative Storyboard application';

-- Display success message
SELECT 'Database tables created successfully!' as message;
