# Database Scripts

This folder contains SQL scripts for setting up and managing the Creative Storyboard database.

## Files

### `init_database.sql`
Complete database initialization script that creates all necessary tables:
- `users` table - stores user account information
- `projects` table - stores project information created by users

### `create_projects_table.sql`
Standalone script to create only the projects table. Use this if you already have the users table set up.

## Usage

### Option 1: Complete Database Setup
Run the complete initialization script:
```bash
mysql -u your_username -p your_database_name < database/init_database.sql
```

### Option 2: Projects Table Only
If you already have the users table, run only the projects table script:
```bash
mysql -u your_username -p your_database_name < database/create_projects_table.sql
```

### Option 3: Using MySQL Workbench or phpMyAdmin
1. Open your MySQL client
2. Select your database
3. Copy and paste the contents of the desired SQL file
4. Execute the script

## Environment Variables

Make sure your `.env` file contains the following database configuration:
```
DB_HOST=localhost
DB_USER=your_mysql_username
DB_PASSWORD=your_mysql_password
DB_NAME=your_database_name
```

## Projects Table Schema

| Column | Type | Description |
|--------|------|-------------|
| `id` | INT AUTO_INCREMENT | Primary key |
| `user_id` | INT | Foreign key to users.id (project owner) |
| `title` | VARCHAR(128) | Project title |
| `description` | VARCHAR(4000) | Project description |
| `created_at` | TIMESTAMP | Creation timestamp |
| `updated_at` | TIMESTAMP | Last update timestamp |
| `image` | VARCHAR(256) | Path to project image file |

## Features

- **Foreign Key Constraints**: Ensures data integrity between users and projects
- **Cascading Deletes**: When a user is deleted, their projects are automatically deleted
- **Indexes**: Optimized for common queries on user_id, created_at, and title
- **Timestamps**: Automatic creation and update timestamps
- **Documentation**: Comprehensive comments on tables and columns

## Notes

- The scripts use `CREATE TABLE IF NOT EXISTS` to prevent errors if tables already exist
- All timestamps use MySQL's `CURRENT_TIMESTAMP` function
- The `image` field stores file paths, not binary data
- Foreign key constraints ensure referential integrity
