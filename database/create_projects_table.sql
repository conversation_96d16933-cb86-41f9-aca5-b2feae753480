-- Create projects table for Creative Storyboard application
-- This table stores project information created by users

CREATE TABLE IF NOT EXISTS projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    title VARCHAR(128) NOT NULL,
    description VARCHAR(4000),
    created_at TIMES<PERSON>MP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    image VARCHAR(256),
    
    -- Foreign key constraint to users table
    CONSTRAINT fk_projects_user_id 
        FOREIGN KEY (user_id) 
        REFERENCES users(id) 
        ON DELETE CASCADE 
        ON UPDATE CASCADE,
    
    -- Index for better query performance
    INDEX idx_user_id (user_id),
    INDEX idx_created_at (created_at)
);

-- Add some comments for documentation
ALTER TABLE projects 
    COMMENT = 'Stores project information created by users in the Creative Storyboard application';

ALTER TABLE projects 
    MODIFY COLUMN id INT AUTO_INCREMENT PRIMARY KEY COMMENT 'Primary key, auto-incrementing project ID',
    MODIFY COLUMN user_id INT NOT NULL COMMENT 'Foreign key reference to users.id - owner of the project',
    MODIFY COLUMN title VARCHAR(128) NOT NULL COMMENT 'Project title, maximum 128 characters',
    MODIFY COLUMN description VARCHAR(4000) COMMENT 'Project description, maximum 4000 characters',
    MODIFY COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Timestamp when project was created',
    MODIFY COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Timestamp when project was last updated',
    MODIFY COLUMN image VARCHAR(256) COMMENT 'File path to project image uploaded by user, maximum 256 characters';
